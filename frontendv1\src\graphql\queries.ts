import { gql } from '@apollo/client';

// Auth Queries

export const ME_QUERY = gql`
  query Me {
    me {
      id
      email
      firstName
      lastName
      phone
      status
      emailConfirmed
      phoneConfirmed
      twoFactorEnabled
      lastLoginAt
      lastLoginIp
      createdAt
      updatedAt
      createdBy
      updatedBy
      role {
        id
        name
        description
        isSystemRole
      }
      tenant {
        id
        name
        createdAt
        updatedAt
      }
    }
  }
`;

export const GET_ACTIVE_SESSIONS_QUERY = gql`
  query GetActiveSessions($userId: Int!) {
    activeSessions(userId: $userId) {
      id
      deviceInfo
      ipAddress
      userAgent
      isActive
      lastActivity
      expiresAt
      createdAt
    }
  }
`;

export const VALIDATE_SESSION_QUERY = gql`
  query ValidateSession($sessionId: String!) {
    validateSession(sessionId: $sessionId)
  }
`;

export const GET_USERS_QUERY = gql`
  query GetUsers($tenantId: Int!, $skip: Int = 0, $take: Int = 50) {
    usersByTenant(tenantId: $tenantId, skip: $skip, take: $take) {
      id
      email
      firstName
      lastName
      phone
      status
      emailConfirmed
      phoneConfirmed
      twoFactorEnabled
      lastLoginAt
      lastLoginIp
      createdAt
      updatedAt
      createdBy
      updatedBy
      role {
        id
        name
        description
        isSystemRole
      }
    }
  }
`;

// Toolbox Queries

export const GET_NON_CLOSED_TOOLBOXES = gql`
  query GetNonClosedToolboxes {
    nonClosedToolbox {
      id
      startDateTime
      status
      emergencyProcedures
      toolboxTrainingTopics
        jobs {
      id
      title
    }
        conductor {
      id
      name
    }
      conductorId
    
      draftedById
      draftedBy {
        id
        name
      }
      draftedDate
      startedById
      startedBy {
        id
        name
      }
      startedDate
      pendingAttendanceById
      pendingAttendanceBy {
        id
        name
      }
      pendingAttendanceDate
      closedById
      closedBy {
        id
        name
      }
      closedDate
      createdAt
      updatedAt
    }
  }
`;

export const GET_TOOLBOX_BY_ID = gql`
  query GetToolboxById($id: Int!) {
    toolboxById(id: $id) {
      id
      startDateTime
      status
      emergencyProcedures
      toolboxTrainingTopics
      conductorId
      conductor {
        id
        name
      }
      attendeePictureFiles {
        id
        fileName
        url
      }
      attendees {
        id
        name
      }
      jobs {
        id
        title
        description
        location
      }
      draftedById
      draftedBy {
        id
        name
      }
      draftedDate
      startedById
      startedBy {
        id
        name
      }
      startedDate
      pendingAttendanceById
      pendingAttendanceBy {
        id
        name
      }
      pendingAttendanceDate
      closedById
      closedBy {
        id
        name
      }
      closedDate
      createdAt
      updatedAt
    }
  }
`;

export const GET_VALID_JOBS = gql`
  query GetValidJobs {
    validJobs {
      id
      title
      description
      location
      status
      startDate
      dueDate
      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
    }
  }
`;

export const GET_ALL_EVENING_TOOLBOXES = gql`
  query GetAllEveningToolboxes {
    allEveningToolboxes {
      id
      jobs {
        jobId
        newHazards {
          description
          controlMeasures
        }
        existingHazards {
          hazardId
          controlMeasures
        }
        isClosed
      }
      closedDate
      conductorId
      conductor {
        id
        name
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_EVENING_TOOLBOX_BY_ID = gql`
  query GetEveningToolboxById($id: Int!) {
    eveningToolboxById(id: $id) {
      id
      jobs {
        jobId
        newHazards {
          description
          controlMeasures
        }
        existingHazards {
          hazardId
          controlMeasures
        }
        isClosed
      }
      closedDate
      conductorId
      conductor {
        id
        name
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_USER_BY_ID_QUERY = gql`
  query GetUserById($userId: Int!) {
    userById(userId: $userId) {
      id
      email
      firstName
      lastName
      phone
      status
      emailConfirmed
      phoneConfirmed
      twoFactorEnabled
      lastLoginAt
      lastLoginIp
      createdAt
      updatedAt
      createdBy
      updatedBy
      role {
        id
        name
        description
        isSystemRole
      }
      tenant {
        id
        name
        createdAt
        updatedAt
      }
    }
  }
`;

export const GET_ROLES_QUERY = gql`
  query GetRoles($tenantId: Int!) {
    rolesByTenant(tenantId: $tenantId) {
      id
      name
      description
      isSystemRole
      permissions {
        id
        resource
        action
        level
        description
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_USERS_BY_ROLE_QUERY = gql`
  query GetUsersByRole($roleId: Int!, $skip: Int = 0, $take: Int = 50) {
    usersByRole(roleId: $roleId, skip: $skip, take: $take) {
      id
      email
      firstName
      lastName
      phone
      avatar
      status
      lastLogin
      lastLoginIp
      failedLoginAttempts
      emailVerified
      createdAt
      updatedAt
      role {
        id
        name
        description
        isSystemRole
      }
    }
  }
`;

// Tenant Queries
export const GET_TENANT = gql`
  query GetTenant($tenantId: ID!) {
    tenant(id: $tenantId) {
      id
      name
      subdomain
      subscriptionPlan
      maxSites
      status
      createdAt
      updatedAt
    }
  }
`;

// Site Queries (tenant-filtered)
export const GET_SITES = gql`
  query GetSites($tenantId: ID!) {
    sites(tenantId: $tenantId) {
      id
      tenantId
      name
      healthStatus
      workersOnSite
      activePermits
      openIncidents
      projectManager
      location
      timeline
      currentPhase
      progressPercentage
      startDate
      endDate
      status
      createdAt
      updatedAt
    }
  }
`;

export const GET_SITE = gql`
  query GetSite($siteId: ID!, $tenantId: ID!) {
    site(id: $siteId, tenantId: $tenantId) {
      id
      tenantId
      name
      healthStatus
      workersOnSite
      activePermits
      openIncidents
      projectManager
      location
      timeline
      currentPhase
      progressPercentage
      startDate
      endDate
      status
      createdAt
      updatedAt
    }
  }
`;

// Worker Queries (tenant-based with site filtering)
export const GET_WORKERS = gql`
  query GetWorkers($tenantId: ID!, $siteId: String) {
    workers(tenantId: $tenantId, siteId: $siteId) {
      id
      tenantId
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      manHours
      profilePictureFile {
        id
      }
      profilePictureUrl
      inductionDate
      medicalCheckDate
      rating
      hireDate
      status
      age
      trainingsCompleted
      trades {
        id
        tenantId
        name
        description
        requiredCertifications
      }
      skills {
        id
        tenantId
        name
        description
        certificationRequired
      }
      trainings {
        id
        tenantId
        name
        description
        duration
        validityPeriodMonths
        trainingType
        trainer
        frequency
        status
      }
      trainingHistory {
        id
        trainingId
        completionDate
        expiryDate
        score
        notes
        status
        training {
          id
          name
        }
      }
      siteAssignments {
        id
        workerId
        siteId
        role
        startDate
        endDate
        status
        hourlyRate
        notes
        site {
          id
          name
        }
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;




// Worker Site Assignment Queries
export const GET_WORKER_SITE_ASSIGNMENTS = gql`
  query GetWorkerSiteAssignments($workerId: ID!, $tenantId: ID!) {
    workerSiteAssignments(workerId: $workerId, tenantId: $tenantId) {
      id
      workerId
      siteId
      role
      startDate
      endDate
      status
      hourlyRate
      notes
      site {
        id
        name
        location
        projectManager
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

// Cross-site Resource Utilization
export const GET_WORKER_UTILIZATION_ACROSS_SITES = gql`
  query GetWorkerUtilizationAcrossSites($tenantId: ID!) {
    workerUtilizationAcrossSites(tenantId: $tenantId) {
      workerId
      workerName
      activeSites
      siteAssignments {
        siteId
        siteName
        role
        startDate
      }
    }
  }
`;

// Training Queries (tenant-based)
export const GET_TRAININGS = gql`
  query GetTrainings($tenantId: ID!) {
    trainings(tenantId: $tenantId) {
      id
      tenantId
      name
      description
      startDate
      endDate
      duration
      validityPeriodMonths
      trainingType
      trainer
      frequency
      status
      workers {
        id
        name
      }
      trainingHistory {
        id
        workerId
        completionDate
        expiryDate
        score
        status
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const GET_TRAINING_EXPIRY_TRACKER = gql`
  query GetTrainingExpiryTracker($siteId: String) {
    trainingExpiryTracker(siteId: $siteId) {
      workerId
      workerName
      trainingId
      trainingName
      completionDate
      expiryDate
      daysUntilExpiry
      status
    }
  }
`;

// Toolbox Session Queries
export const GET_TOOLBOX_SESSIONS = gql`
  query GetToolboxSessions($siteId: String, $dateFrom: String, $dateTo: String) {
    toolboxSessions(siteId: $siteId, dateFrom: $dateFrom, dateTo: $dateTo) {
      id
      sessionTime
      topic
      conductor
      photoUrl
      notes
      attendances {
        id
        workerId
        worker {
          id
          name
          photoUrl
        }
        wasPresent
        notes
        createdAt
        createdBy
        updatedAt
        updatedBy
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

// Toolbox Queries
export const GET_ALL_TOOLBOXES = gql`
  query GetAllToolboxes($where: ToolboxFilterInput) {
    allToolboxes(where: $where) {
      id
      date
      status
      emergencyProcedures
      toolboxTrainingTopics
      closedDate
      conductor {
        workerId
        name
        signatureFileId
      }
      attendees {
        workerId
        name
        designation
        signatureFileId
      }
      jobs {
        id
        title
        description
        hazards {
          id
          description
          controlMeasures {
            id
            description
            closed
          }
        }
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;



export const GET_TODAYS_TOOLBOX = gql`
  query GetTodaysToolbox {
    todaysToolbox {
      id
      date
      status
      emergencyProcedures
      toolboxTrainingTopics
      closedDate
      conductor {
        workerId
        name
        signatureFileId
      }
      attendees {
        workerId
        name
        designation
        signatureFileId
      }
      jobs {
        id
        title
        description
        hazards {
          id
          description
          controlMeasures {
            id
            description
            closed
          }
        }
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const GET_TODAYS_JOB_RISK_ASSESSMENT = gql`
  query GetTodaysJobRiskAssessment {
    todaysJobRiskAssessment {
      id
      title
      hazards {
        id
        description
        controlMeasures {
          id
          description
        }
      }
    }
  }
`;

export const GET_TOOLBOX_RISK_ASSESSMENT = gql`
  query GetToolboxRiskAssessment($jobId: Int!) {
    toolboxRiskAssessment(jobId: $jobId) {
      id
      title
      hazards {
        id
        description
        controlMeasures {
          id
          description
        }
      }
    }
  }
`;

// Worker Attendance Queries
export const GET_WORKER_ATTENDANCE = gql`
  query GetWorkerAttendance($workerId: Int!, $dateFrom: String, $dateTo: String) {
    workerAttendance(workerId: $workerId, dateFrom: $dateFrom, dateTo: $dateTo) {
      id
      workerId
      worker {
        id
        name
        photoUrl
      }
      checkInTime
      checkOutTime
      status
      notes
      isVerifiedByHikvision
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

// Trades and Skills Queries (tenant-based)
export const GET_TRADES = gql`
  query GetTrades($tenantId: ID!) {
    trades(tenantId: $tenantId) {
      id
      tenantId
      name
      description
      requiredCertifications
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const GET_SKILLS = gql`
  query GetSkills($tenantId: ID!) {
    skills(tenantId: $tenantId) {
      id
      tenantId
      name
      description
      certificationRequired
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

// Queries for worker form dropdowns - matching schema exactly
export const GET_ALL_TRAININGS = gql`
  query GetAllTrainings {
    allTrainings {
      id
      name
    }
  }
`;

export const GET_ALL_TRADES = gql`
  query GetAllTrades {
    allTrades {
      id
      name
    }
  }
`;

export const GET_ALL_SKILLS = gql`
  query GetAllSkills {
    allSkills {
      id
      name
    }
  }
`;

// Worker queries matching schema
// Enhanced Worker Management Queries for Centralized Company-Level Architecture

export const GET_ALL_COMPANY_WORKERS = gql`
  query GetAllCompanyWorkers {
    allCompanyWorkers {
      id
      tenantId
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      profilePictureFile {
        id
      }
      profilePictureUrl
      age
      trainingsCompleted
      manHours
      rating
      status

      # Company-level specific fields
      employeeNumber
      complianceStatus
      currentSiteId
      totalSiteAssignments
      lastAssignmentDate
      hireDate
      terminationDate
      rehireEligible
      performanceRating
      notes

      # Related data
      trades {
        id
        name
        description
        requiredCertifications
      }
      skills {
        id
        name
        description
        certificationRequired
      }
      trainings {
        id
        name
        description
        validityPeriodMonths
      }

      # Site assignments
      siteAssignments {
        id
        siteId
        role
        startDate
        endDate
        status
        assignmentType
        assignmentReason
        assignedBy
        hourlyRate
        overtimeRate
        totalHoursWorked
        attendanceRate
        performanceRating
        performanceNotes
      }

      # Training compliance
      trainingCompliance {
        id
        trainingId
        trainingName
        tradeId
        tradeName
        complianceStatus
        isRequired
        isMandatory
        completionDate
        expiryDate
        renewalDueDate
        certificateUrl
        blockingSiteAssignment
        notes
      }

      createdAt
      createdBy
      updatedAt
      updatedBy
      isDeleted
    }
  }
`;

export const GET_ALL_WORKERS = gql`
  query GetAllWorkers {
    allWorkers {
      id
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      profilePictureFile {
        id
      }
      profilePictureUrl
      age
      trainingsCompleted
      manHours
      rating
      trades {
        id
        name
      }
      skills {
        id
        name
      }
      trainings {
        id
        name
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
      isDeleted
    }
  }
`;

// Site-specific worker queries
export const GET_SITE_WORKERS = gql`
  query GetSiteWorkers($siteId: String!) {
    siteWorkers(siteId: $siteId) {
      workerId
      workerName
      employeeNumber
      currentRole
      assignmentDate
      assignmentStatus
      primaryTrade
      complianceStatus
      isOnSite
      lastCheckIn
      hoursWorkedToday
      hoursWorkedThisWeek
      attendanceRate
      safetyScore
      photoUrl

      # Full worker details
      worker {
        id
        name
        nationalId
        phoneNumber
        email
        dateOfBirth
        gender
        profilePictureUrl
        trades {
          id
          name
          description
        }
        skills {
          id
          name
          description
        }
        trainingCompliance {
          id
          trainingName
          complianceStatus
          blockingSiteAssignment
          expiryDate
        }
      }
    }
  }
`;

export const GET_AVAILABLE_WORKERS_FOR_SITE = gql`
  query GetAvailableWorkersForSite($siteId: String!) {
    availableWorkersForSite(siteId: $siteId) {
      id
      name
      employeeNumber
      primaryTrade
      complianceStatus
      canBeAssigned
      blockingReasons
      profilePictureUrl
      performanceRating
      totalSiteAssignments
      lastAssignmentDate

      trades {
        id
        name
        description
      }

      trainingCompliance {
        id
        trainingName
        complianceStatus
        blockingSiteAssignment
        expiryDate
        renewalDueDate
      }
    }
  }
`;

export const GET_COMPANY_WORKER_STATS = gql`
  query GetCompanyWorkerStats {
    companyWorkerStats {
      totalWorkers
      activeWorkers
      workersOnSites
      availableWorkers
      compliantWorkers
      nonCompliantWorkers
      workersNeedingTraining
      workersByTrade
      workersBySite
      averageExperience
      averagePerformanceRating
      totalManHours
      averageHourlyRate
    }
  }
`;

export const GET_WORKER_BY_ID = gql`
  query GetWorkerById($id: Int!) {
    workerById(id: $id) {
      id
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      profilePictureFile {
        id
      }
      profilePictureUrl
      age
      trainingsCompleted
      manHours
      rating
      trades {
        id
        name
        description
      }
      skills {
        id
        name
        description
      }
      trainings {
        id
        name
        description
        duration
        validityPeriodMonths
        trainingType
        trainer
        frequency
        status
      }
      trainingHistory {
        id
        workerId
        trainingId
        training {
          id
          name
        }
        completionDate
        expiryDate
        status
        notes
        score
        isExpired
        isExpiringSoon
        daysUntilExpiry
      }
      incidents {
        id
        title
        description
        incidentDate
        location
        status
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
      isDeleted
    }
  }
`;

// Audit Log Queries
export const GET_AUDIT_LOGS = gql`
  query GetAuditLogs($entityType: String!, $entityId: Int!, $limit: Int, $offset: Int) {
    auditLogs(entityType: $entityType, entityId: $entityId, limit: $limit, offset: $offset) {
      id
      entityType
      entityId
      action
      fieldChanges {
        field
        oldValue
        newValue
      }
      performedBy
      performedAt
      ipAddress
      userAgent
      notes
    }
  }
`;

// Notification Queries
export const GET_MY_NOTIFICATIONS = gql`
  query GetMyNotifications($skip: Int = 0, $take: Int = 50, $unreadOnly: Boolean = false) {
    myNotifications(skip: $skip, take: $take, unreadOnly: $unreadOnly) {
      id
      type
      title
      message
      priority
      status
      entity
      operation
      metadata
      sentAt
      readAt
      expiresAt
      actionUrl
      actionLabel
      userId
      tenantId
      createdAt
      createdBy
      updatedAt
      updatedBy
      deliveries {
        id
        channel
        status
        recipient
        sentAt
        deliveredAt
        failedAt
        errorMessage
        retryCount
        nextRetryAt
      }
    }
  }
`;

export const GET_UNREAD_NOTIFICATION_COUNT = gql`
  query GetUnreadNotificationCount {
    unreadNotificationCount
  }
`;

export const GET_NOTIFICATION_PREFERENCES = gql`
  query GetNotificationPreferences($notificationType: String!) {
    notificationPreferences(notificationType: $notificationType) {
      id
      notificationType
      inAppEnabled
      emailEnabled
      smsEnabled
      minimumPriority
      doNotDisturbEnabled
      doNotDisturbStart
      doNotDisturbEnd
      userId
      createdAt
      updatedAt
    }
  }
`;

export const GET_ALL_NOTIFICATION_PREFERENCES = gql`
  query GetAllNotificationPreferences {
    allNotificationPreferences {
      id
      notificationType
      inAppEnabled
      emailEnabled
      smsEnabled
      minimumPriority
      doNotDisturbEnabled
      doNotDisturbStart
      doNotDisturbEnd
      userId
      createdAt
      updatedAt
    }
  }
`;

// Inspection Queries
export const GET_INSPECTIONS = gql`
  query GetInspections($skip: Int = 0, $take: Int = 50) {
    inspections(skip: $skip, take: $take) {
      id
      approved
      comments
      inspectionType
      inspectedById
      inspectedBy {
        id
        name
      }
      signatureFileId
      signatureFile {
        id
        fileName
        url
      }
      inspectionItems {
        id
        description
        isTrue
        remarks
        imageFiles {
          id
          fileName
          url
          contentType
        }
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const GET_INSPECTION_BY_ID = gql`
  query GetInspectionById($id: Int!) {
    inspectionById(id: $id) {
      id
      approved
      comments
      inspectionType
      inspectedById
      inspectedBy {
        id
        name
      }
      signatureFileId
      signatureFile {
        id
        fileName
        url
      }
      inspectionItems {
        id
        description
        isTrue
        remarks
        imageFiles {
          id
          fileName
          url
          contentType
          size
        }
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const GET_INSPECTIONS_BY_INSPECTOR = gql`
  query GetInspectionsByInspector($inspectorId: Int!) {
    inspectionsByInspector(inspectorId: $inspectorId) {
      id
      approved
      comments
      inspectionType
      inspectedById
      inspectedBy {
        id
        name
      }
      inspectionItems {
        id
        description
        isTrue
        remarks
        imageFiles {
          id
          fileName
          url
        }
      }
      createdAt
      createdBy
    }
  }
`;

// Training Assignment Mutations
export const ASSIGN_TRAINING = gql`
  mutation AssignTraining($input: AssignTrainingInput!) {
    assignTraining(input: $input) {
      success
      message
      assignedCount
    }
  }
`;

export const RECORD_TRAINING_COMPLETION = gql`
  mutation RecordTrainingCompletion($input: RecordTrainingCompletionInput!) {
    recordTrainingCompletion(input: $input) {
      id
      workerId
      trainingId
      completionDate
      expiryDate
      score
      status
      certificateUrl
      notes
      createdAt
      createdBy
    }
  }
`;

// Training Program Management Mutations
export const CREATE_TRAINING_PROGRAM = gql`
  mutation CreateTrainingProgram($input: CreateTrainingProgramInput!) {
    createTrainingProgram(input: $input) {
      id
      tenantId
      name
      description
      validityPeriodMonths
      trainingType
      isMandatory
      requiredCertifications
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TRAINING_PROGRAM = gql`
  mutation UpdateTrainingProgram($id: ID!, $input: UpdateTrainingProgramInput!) {
    updateTrainingProgram(id: $id, input: $input) {
      id
      tenantId
      name
      description
      validityPeriodMonths
      trainingType
      isMandatory
      requiredCertifications
      updatedAt
      updatedBy
    }
  }
`;

export const DELETE_TRAINING_PROGRAM = gql`
  mutation DeleteTrainingProgram($id: ID!) {
    deleteTrainingProgram(id: $id) {
      success
      message
    }
  }
`;

// Bulk Training Assignment
export const BULK_ASSIGN_TRAINING = gql`
  mutation BulkAssignTraining($input: BulkAssignTrainingInput!) {
    bulkAssignTraining(input: $input) {
      success
      assignedCount
      failedCount
      message
      errors {
        workerId
        error
      }
    }
  }
`;

// Auto Assignment by Trade
export const AUTO_ASSIGN_TRAINING_BY_TRADE = gql`
  mutation AutoAssignTrainingByTrade($input: AutoAssignTrainingByTradeInput!) {
    autoAssignTrainingByTrade(input: $input) {
      success
      assignedCount
      message
      assignedWorkers {
        id
        name
        tradeId
        tradeName
      }
    }
  }
`;

// Job/Task Management Queries
export const GET_ALL_JOBS = gql`
  query GetAllJobs {
    allJobs {
      id
      title
      description
      location
      status
      requiredPermits
      timeForCompletion
      startDate
      dueDate
      calculatedDueDate
      categoryId
      category {
        id
        description
      }
      requestedById
      requestedBy {
        id
        name
      }
      requestedDate
      blockedById
      blockedBy {
        id
        name
      }
      blockedDate
      reviewedById
      reviewedBy {
        id
        name
      }
      reviewedDate
      approvedById
      approvedBy {
        id
        name
      }
      approvedDate
      finishedById
      finishedBy {
        id
        name
      }
      finishedDate
      chiefEngineerId
      chiefEngineer {
        id
        name
      }
      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
      documents {
        id
        name
        url
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
      isDeleted
    }
  }
`;

export const GET_REQUESTED_JOBS = gql`
  query GetRequestedJobs {
    requestedJobs {
      id
      title
      description
      status
      requiredPermits
      timeForCompletion
      startDate
      dueDate
      calculatedDueDate
      requestedById
      requestedBy {
        id
        name
      }
      requestedDate
      chiefEngineerId
      chiefEngineer {
        id
        name
      }
      workers {
        id
        name
        company
      }
      createdAt
      createdBy
    }
  }
`;

export const GET_PENDING_APPROVAL_JOBS = gql`
  query GetPendingApprovalJobs {
    pendingApprovalJobs {
      id
      title
      description
      status
      requiredPermits
      timeForCompletion
      startDate
      dueDate
      calculatedDueDate
      requestedById
      requestedBy {
        id
        name
      }
      requestedDate
      reviewedById
      reviewedBy {
        id
        name
      }
      reviewedDate
      chiefEngineerId
      chiefEngineer {
        id
        name
      }
      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
      documents {
        id
        name
        url
      }
      createdAt
      createdBy
    }
  }
`;

export const GET_APPROVED_JOBS = gql`
  query GetApprovedJobs {
    approvedJobs {
      id
      title
      description
      status
      requiredPermits
      timeForCompletion
      startDate
      dueDate
      calculatedDueDate
      requestedById
      requestedBy {
        id
        name
      }
      requestedDate
      reviewedById
      reviewedBy {
        id
        name
      }
      reviewedDate
      approvedById
      approvedBy {
        id
        name
      }
      approvedDate
      chiefEngineerId
      chiefEngineer {
        id
        name
      }
      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
      documents {
        id
        name
        url
      }
      createdAt
      createdBy
    }
  }
`;

export const GET_JOB_BY_ID = gql`
  query GetJobById($id: Int!) {
    jobById(id: $id) {
      id
      title
      description
      status
      requiredPermits
      timeForCompletion
      startDate
      dueDate
      calculatedDueDate
      categoryId
      category {
        id
        description
      }
      requestedById
      requestedBy {
        id
        name
      }
      requestedDate
      blockedById
      blockedBy {
        id
        name
      }
      blockedDate
      reviewedById
      reviewedBy {
        id
        name
      }
      reviewedDate
      approvedById
      approvedBy {
        id
        name
      }
      approvedDate
      finishedById
      finishedBy {
        id
        name
      }
      finishedDate
      chiefEngineerId
      chiefEngineer {
        id
        name
      }

      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
      documents {
        id
        name
        url
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
      isDeleted
    }
  }
`;
