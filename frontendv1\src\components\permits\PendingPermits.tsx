import React, { useState, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@apollo/client";
import { Clock, FileText, Flame, HardHat, Shovel, Shield, Zap } from "lucide-react";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";
import { GET_JOBS_WITH_PENDING_PERMITS } from "../../graphql/queries";

interface PendingPermitsProps {
  siteId: string;
}

interface JobWithPendingPermits {
  id: number;
  title: string;
  description: string;
  location: string;
  status: string;
  requiredPermits: string[];
  startDate: string;
  dueDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  createdAt: string;
}

interface PendingPermitItem {
  id: string;
  jobId: number;
  jobTitle: string;
  permitType: string;
  permitTypeName: string;
  description: string;
  location: string;
  requestedBy: string;
  scheduledDate: string;
  icon: React.ReactNode;
  color: string;
}



const PendingPermits: React.FC<PendingPermitsProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const { siteId: paramSiteId } = useParams();
  const currentSiteId = siteId || paramSiteId;

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPermitType, setSelectedPermitType] = useState("all");

  // Fetch jobs with pending permits
  const { data, loading, error } = useQuery<{ jobsWithPendingPermits: JobWithPendingPermits[] }>(
    GET_JOBS_WITH_PENDING_PERMITS
  );

  // Permit type filter options for the new search component
  const permitTypeFilters: TagOption[] = [
    { id: "GENERAL_WORK_PERMIT", name: "General Work" },
    { id: "HOT_WORK_PERMIT", name: "Hot Work" },
    { id: "CONFINED_SPACE_ENTRY_PERMIT", name: "Confined Space" },
    { id: "WORK_AT_HEIGHT_PERMIT", name: "Work at Height" },
    { id: "EXCAVATION_PERMIT", name: "Excavation" }
  ];

  // Helper function to get permit type icon and color
  const getPermitTypeConfig = (permitType: string) => {
    switch (permitType) {
      case "GENERAL_WORK_PERMIT":
        return { icon: <FileText className="h-5 w-5" />, color: "text-gray-600", name: "General Work" };
      case "HOT_WORK_PERMIT":
        return { icon: <Flame className="h-5 w-5" />, color: "text-red-600", name: "Hot Work" };
      case "CONFINED_SPACE_ENTRY_PERMIT":
        return { icon: <Shield className="h-5 w-5" />, color: "text-blue-600", name: "Confined Space" };
      case "WORK_AT_HEIGHT_PERMIT":
        return { icon: <HardHat className="h-5 w-5" />, color: "text-orange-600", name: "Work at Height" };
      case "EXCAVATION_PERMIT":
        return { icon: <Shovel className="h-5 w-5" />, color: "text-yellow-600", name: "Excavation" };
      default:
        return { icon: <FileText className="h-5 w-5" />, color: "text-gray-600", name: "General Work" };
    }
  };

  // Convert jobs with pending permits to permit items
  const pendingPermitItems: PendingPermitItem[] = useMemo(() => {
    if (!data?.jobsWithPendingPermits) return [];

    const items: PendingPermitItem[] = [];

    data.jobsWithPendingPermits.forEach(job => {
      job.requiredPermits.forEach(permitType => {
        const config = getPermitTypeConfig(permitType);
        items.push({
          id: `${job.id}-${permitType}`,
          jobId: job.id,
          jobTitle: job.title,
          permitType,
          permitTypeName: config.name,
          description: job.description,
          location: job.location,
          requestedBy: job.requestedBy.name,
          scheduledDate: job.startDate,
          icon: config.icon,
          color: config.color
        });
      });
    });

    return items;
  }, [data]);

  // Filter pending permit items based on search and permit type
  const filteredItems = useMemo(() => {
    return pendingPermitItems.filter(item => {
      const matchesSearch = searchQuery === "" ||
        item.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.requestedBy.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesPermitType = selectedPermitType === "all" ||
        item.permitType === selectedPermitType;

      return matchesSearch && matchesPermitType;
    });
  }, [pendingPermitItems, searchQuery, selectedPermitType]);

  // Handle permit type click
  const handlePermitClick = (item: PendingPermitItem) => {
    const permitTypeRoutes: { [key: string]: string } = {
      "GENERAL_WORK_PERMIT": "general-work",
      "HOT_WORK_PERMIT": "hot-work",
      "CONFINED_SPACE_ENTRY_PERMIT": "confined-space",
      "WORK_AT_HEIGHT_PERMIT": "work-at-height",
      "EXCAVATION_PERMIT": "excavation"
    };

    const route = permitTypeRoutes[item.permitType] || "general-work";

    if (currentSiteId) {
      navigate(`/sites/${currentSiteId}/permits/${route}/form/${item.jobId}`);
    } else {
      navigate(`/${route}/form/${item.jobId}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading pending permits...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Error loading pending permits: {error.message}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Pending Permits</h2>
          <p className="text-sm text-gray-600">Jobs requiring permit generation. Click on a permit type to create the permit.</p>
        </div>
        <div className="text-sm text-gray-600">
          Total {filteredItems.length} permits
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search by job name, description, engineer, or location..."
        tags={[{ id: "all", name: "All" }, ...permitTypeFilters]}
        selectedTagId={selectedPermitType}
        onTagChange={setSelectedPermitType}
      />

      {/* Pending Permits Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredItems.map((item) => (
          <div
            key={item.id}
            onClick={() => handlePermitClick(item)}
            className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className={item.color}>
                  {item.icon}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 text-sm">{item.permitTypeName}</h3>
                  <p className="text-xs text-gray-500">for {item.jobTitle}</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>

              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>{item.location}</span>
                <span>{new Date(item.scheduledDate).toLocaleDateString()}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">Requested by {item.requestedBy}</span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Pending
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <Clock className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pending permits</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchQuery || selectedPermitType !== "all"
              ? "No permits match your current filters."
              : "All jobs have their required permits generated."
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default PendingPermits;
